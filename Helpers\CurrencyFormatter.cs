using System;
using System.Globalization;

namespace DriverManagementSystem.Helpers
{
    /// <summary>
    /// مساعد تنسيق العملة - حل جذري موحد لجميع المبالغ في النظام
    /// </summary>
    public static class CurrencyFormatter
    {
        /// <summary>
        /// تنسيق المبلغ بالفواصل
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ منسق بالفواصل</returns>
        public static string Format(decimal amount)
        {
            try
            {
                return amount.ToString("#,##0", CultureInfo.InvariantCulture);
            }
            catch
            {
                return amount.ToString();
            }
        }

        /// <summary>
        /// تنسيق المبلغ بالفواصل مع إضافة "ريال"
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ منسق بالفواصل مع "ريال"</returns>
        public static string FormatWithCurrency(decimal amount)
        {
            try
            {
                return amount.ToString("#,##0", CultureInfo.InvariantCulture) + " ريال";
            }
            catch
            {
                return amount.ToString() + " ريال";
            }
        }

        /// <summary>
        /// تنسيق المبلغ من نص
        /// </summary>
        /// <param name="amountText">النص المحتوي على المبلغ</param>
        /// <returns>المبلغ منسق بالفواصل</returns>
        public static string FormatFromText(string amountText)
        {
            try
            {
                if (string.IsNullOrEmpty(amountText))
                    return "0";

                // إزالة الفواصل والنصوص الإضافية
                var cleanText = amountText.Replace(",", "")
                                         .Replace("ريال", "")
                                         .Replace("يمني", "")
                                         .Trim();

                if (decimal.TryParse(cleanText, out decimal amount))
                {
                    return Format(amount);
                }

                return "0";
            }
            catch
            {
                return "0";
            }
        }

        /// <summary>
        /// تنسيق المبلغ من نص مع إضافة "ريال"
        /// </summary>
        /// <param name="amountText">النص المحتوي على المبلغ</param>
        /// <returns>المبلغ منسق بالفواصل مع "ريال"</returns>
        public static string FormatFromTextWithCurrency(string amountText)
        {
            try
            {
                if (string.IsNullOrEmpty(amountText))
                    return "0 ريال";

                // إزالة الفواصل والنصوص الإضافية
                var cleanText = amountText.Replace(",", "")
                                         .Replace("ريال", "")
                                         .Replace("يمني", "")
                                         .Trim();

                if (decimal.TryParse(cleanText, out decimal amount))
                {
                    return FormatWithCurrency(amount);
                }

                return "0 ريال";
            }
            catch
            {
                return "0 ريال";
            }
        }

        /// <summary>
        /// تحويل النص إلى مبلغ رقمي
        /// </summary>
        /// <param name="amountText">النص المحتوي على المبلغ</param>
        /// <returns>المبلغ كرقم</returns>
        public static decimal ParseAmount(string amountText)
        {
            try
            {
                if (string.IsNullOrEmpty(amountText))
                    return 0;

                // إزالة الفواصل والنصوص الإضافية
                var cleanText = amountText.Replace(",", "")
                                         .Replace("ريال", "")
                                         .Replace("يمني", "")
                                         .Trim();

                if (decimal.TryParse(cleanText, out decimal amount))
                {
                    return amount;
                }

                return 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// التحقق من صحة المبلغ
        /// </summary>
        /// <param name="amountText">النص المحتوي على المبلغ</param>
        /// <returns>true إذا كان المبلغ صحيح</returns>
        public static bool IsValidAmount(string amountText)
        {
            try
            {
                if (string.IsNullOrEmpty(amountText))
                    return false;

                var cleanText = amountText.Replace(",", "")
                                         .Replace("ريال", "")
                                         .Replace("يمني", "")
                                         .Trim();

                return decimal.TryParse(cleanText, out decimal amount) && amount >= 0;
            }
            catch
            {
                return false;
            }
        }
    }
}
