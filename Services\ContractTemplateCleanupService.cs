using System;
using System.Linq;
using System.Threading.Tasks;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة تنظيف قوالب العقود - حذف جميع القوالب وإنشاء قالب واحد صحيح فقط
    /// </summary>
    public static class ContractTemplateCleanupService
    {
        /// <summary>
        /// تنظيف شامل لقوالب العقود - حذف الكل وإنشاء قالب واحد صحيح
        /// </summary>
        public static async Task CleanupAndCreateSingleTemplateAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🧹 بدء تنظيف قوالب العقود...");

                var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
                var connectionString = @"Server=(localdb)\MSSQLLocalDB;Database=DriverManagementDB;Trusted_Connection=true;MultipleActiveResultSets=true";
                optionsBuilder.UseSqlServer(connectionString);

                using var context = new ApplicationDbContext(optionsBuilder.Options);

                // الخطوة 1: حذف جميع القوالب الموجودة
                await DeleteAllExistingTemplatesAsync(context);

                // الخطوة 2: إنشاء قالب واحد صحيح فقط
                await CreateSingleCorrectTemplateAsync(context);

                System.Diagnostics.Debug.WriteLine("✅ تم تنظيف قوالب العقود بنجاح - يوجد الآن قالب واحد صحيح فقط");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف قوالب العقود: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// حذف جميع القوالب الموجودة
        /// </summary>
        private static async Task DeleteAllExistingTemplatesAsync(ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🗑️ حذف جميع القوالب الموجودة...");

                // جلب جميع القوالب
                var existingTemplates = await context.ContractTemplates.ToListAsync();

                if (existingTemplates.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"📋 تم العثور على {existingTemplates.Count} قالب للحذف");

                    // حذف جميع القوالب
                    context.ContractTemplates.RemoveRange(existingTemplates);
                    await context.SaveChangesAsync();

                    System.Diagnostics.Debug.WriteLine($"✅ تم حذف {existingTemplates.Count} قالب بنجاح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ لا توجد قوالب للحذف");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف القوالب: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء قالب واحد صحيح فقط
        /// </summary>
        private static async Task CreateSingleCorrectTemplateAsync(ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📝 إنشاء قالب العقد الصحيح الوحيد...");

                var correctTemplate = new ContractTemplate
                {
                    // مقدمة العقد
                    ContractIntroduction = "أنه في يوم {ContractDateArabic} الموافق {ContractDate} م بأمانة العاصمة صنعاء، تم إبرام هذا العقد بين كل من:",

                    // الطرف الأول (السائق)
                    FirstPartyTemplate = "الطرف الأول: {DriverName}، رقم البطاقة الشخصية: {NationalId}، مكان الإصدار: {IssuePlace}، تاريخ الإصدار: {IssueDate}، رقم الهاتف: {PhoneNumber}",

                    // الطرف الثاني (الصندوق)
                    SecondPartyTemplate = "الطرف الثاني: الصندوق الاجتماعي للتنمية - المركز الرئيسي ومقره شارع الشهيد إبراهيم الحمدي (شارع الستين الجنوبي) مقابل الجهاز المركزي للرقابة والمحاسبة، ويمثله في هذا العقد الأستاذ عبدالله علي الديلمي بصفته المدير التنفيذي للصندوق.",

                    // مواصفات المركبة
                    VehicleSpecsTemplate = "أجر الطرف الأول للطرف الثاني سيارة بالمواصفات التالية: نوع السيارة: {VehicleType}، سنة الصنع: {ManufactureYear}، لون السيارة: {VehicleColor}، رقم اللوحة: {VehicleNumber}، رقم رخصة التسيير: {LicenseNumber}، تاريخ إصدار الرخصة: {LicenseIssueDate}",

                    // الغرض من العقد
                    PurposeTemplate = "اتفق الطرفان على استئجار الطرف الثاني وسيلة نقل (سيارة مع سائقها) لمرافقة الأخ/{VisitConductor}، صفته {VisitConductorRank} أثناء تنفيذ المهام الميدانية المطلوبة منه.",

                    // مدة العقد
                    DurationTemplate = "تاريخ بداية المهمة: {StartDateArabic} الموافق {StartDate}م، تاريخ انتهاء المهمة: {EndDateArabic} الموافق {EndDate}م، إجمالي عدد الأيام: {DaysCount} أيام. تسير المهمة بموجب وثيقة خطة تنفيذ المهمة المرفقة بالعقد.",

                    // السعر - النص الصحيح الوحيد
                    PriceTemplate = "اتفق الطرفان على أن القيمة الإيجارية لوسيلة النقل خلال فترة تنفيذ المهمة بإكملها مبلغ وقدره ({TotalPrice}) ريال يمني، وتدفع من الطرف الثاني للطرف الأول وفقاً للإجراءات المالية للصندوق وتصرف بحسب عدد الأيام الفعلية المنجزة.",

                    // إقرار الملكية
                    OwnershipTemplate = "يقر الطرف الأول بموجب هذا العقد بأنه المالك الشرعي لوسيلة النقل، وأنه حائز على وثائق إثبات الملكية الشرعية سارية المفعول وأنها خالية من أي التزامات شخصية أو عينية أو مطالبات أو حقوق للغير.",

                    // التزامات الطرف الأول
                    ObligationsTemplate = "يلتزم الطرف الأول بموجب هذا العقد بأن ينفذ العقد بنفسه وأن يبذل في تأديته من العناية مايبذله الشخص المعتاد، والامتثال بأخلاقيات ومدونة سلوك الصندوق الاجتماعي للتنمية أثناء الرحلة، والحرص على سلامة الركاب والالتزام بكافة قواعد وآداب المرور وقانونه، وعدم نقل أي ركاب آخرين عدا الطرف الثاني أثناء أداء المهام الميدانية، واصطحاب كافة وثائق الملكية أثناء تنفيذ المهمة، والصيانة الدورية لوسيلة النقل وفحصها قبل تنفيذ المهمة، وسرعة الإبلاغ عن أي حوادث تحدث أثناء قيادة المركبة، واصطحاب أدوات السلامة مثل طفاية الحريق وصندوق إسعافات أولية ومثلث التحذير وعدة صيانة السيارة.",

                    // تواريخ النظام
                    CreatedDate = DateTime.Now,
                    LastModified = DateTime.Now
                };

                // إضافة القالب الجديد
                context.ContractTemplates.Add(correctTemplate);
                await context.SaveChangesAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قالب العقد الصحيح الوحيد بنجاح");
                System.Diagnostics.Debug.WriteLine($"📋 معرف القالب الجديد: {correctTemplate.Id}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء القالب الجديد: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// التحقق من وجود قالب واحد صحيح فقط
        /// </summary>
        public static async Task<bool> VerifySingleCorrectTemplateAsync()
        {
            try
            {
                var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
                var connectionString = @"Server=(localdb)\MSSQLLocalDB;Database=DriverManagementDB;Trusted_Connection=true;MultipleActiveResultSets=true";
                optionsBuilder.UseSqlServer(connectionString);

                using var context = new ApplicationDbContext(optionsBuilder.Options);

                var templates = await context.ContractTemplates.ToListAsync();

                if (templates.Count == 1)
                {
                    var template = templates.First();
                    bool isCorrect = !string.IsNullOrEmpty(template.PriceTemplate) &&
                                   template.PriceTemplate.Contains("{TotalPrice}") &&
                                   !template.PriceTemplate.Contains("35200") &&
                                   !template.PriceTemplate.Contains("35,200");

                    System.Diagnostics.Debug.WriteLine($"✅ يوجد قالب واحد فقط - صحيح: {isCorrect}");
                    return isCorrect;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ يوجد {templates.Count} قالب (يجب أن يكون واحد فقط)");
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من القالب: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تقرير حالة قوالب العقود
        /// </summary>
        public static async Task<string> GetTemplatesStatusReportAsync()
        {
            try
            {
                var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
                var connectionString = @"Server=(localdb)\MSSQLLocalDB;Database=DriverManagementDB;Trusted_Connection=true;MultipleActiveResultSets=true";
                optionsBuilder.UseSqlServer(connectionString);

                using var context = new ApplicationDbContext(optionsBuilder.Options);

                var templates = await context.ContractTemplates.ToListAsync();

                var report = "📊 تقرير حالة قوالب العقود\n";
                report += "================================\n\n";
                report += $"📋 عدد القوالب الموجودة: {templates.Count}\n\n";

                if (templates.Count == 0)
                {
                    report += "❌ لا توجد قوالب عقود\n";
                    report += "💡 يجب إنشاء قالب جديد\n";
                }
                else if (templates.Count == 1)
                {
                    var template = templates.First();
                    report += "✅ يوجد قالب واحد فقط (مطلوب)\n\n";
                    report += $"📄 معرف القالب: {template.Id}\n";
                    report += $"📅 تاريخ الإنشاء: {template.CreatedDate:yyyy-MM-dd HH:mm}\n";
                    report += $"📅 آخر تعديل: {template.LastModified:yyyy-MM-dd HH:mm}\n\n";

                    // فحص صحة القالب
                    if (string.IsNullOrEmpty(template.PriceTemplate))
                    {
                        report += "❌ قالب السعر فارغ\n";
                    }
                    else if (template.PriceTemplate.Contains("{TotalPrice}"))
                    {
                        if (template.PriceTemplate.Contains("35200") || template.PriceTemplate.Contains("35,200"))
                        {
                            report += "⚠️ القالب يحتوي على {TotalPrice} ولكن أيضاً على مبلغ ثابت\n";
                        }
                        else
                        {
                            report += "✅ القالب صحيح - يحتوي على {TotalPrice} فقط\n";
                        }
                    }
                    else
                    {
                        report += "❌ القالب لا يحتوي على {TotalPrice}\n";
                    }
                }
                else
                {
                    report += $"⚠️ يوجد {templates.Count} قالب (أكثر من المطلوب)\n";
                    report += "💡 يُنصح بالاحتفاظ بقالب واحد فقط\n\n";

                    for (int i = 0; i < templates.Count; i++)
                    {
                        var template = templates[i];
                        report += $"📄 القالب {i + 1} (ID: {template.Id}):\n";
                        report += $"   📅 تاريخ الإنشاء: {template.CreatedDate:yyyy-MM-dd HH:mm}\n";

                        if (template.PriceTemplate?.Contains("{TotalPrice}") == true)
                        {
                            report += "   ✅ يحتوي على {TotalPrice}\n";
                        }
                        else
                        {
                            report += "   ❌ لا يحتوي على {TotalPrice}\n";
                        }

                        if (template.PriceTemplate?.Contains("35200") == true)
                        {
                            report += "   ⚠️ يحتوي على مبلغ ثابت\n";
                        }

                        report += "\n";
                    }
                }

                return report;
            }
            catch (Exception ex)
            {
                return $"❌ خطأ في إنشاء التقرير: {ex.Message}";
            }
        }
    }
}
