using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة حذف المبلغ الثابت من الأقواس فقط
    /// </summary>
    public static class RemoveFixedAmountService
    {
        /// <summary>
        /// حذف المبلغ الثابت من داخل الأقواس
        /// </summary>
        public static async Task<string> RemoveFixedAmountFromBracketsAsync()
        {
            try
            {
                using var context = new ApplicationDbContext();
                var report = "🗑️ بدء عملية حذف المبلغ الثابت من الأقواس\n";
                report += "==========================================\n\n";

                // جلب جميع القوالب
                var templates = await context.ContractTemplates.ToListAsync();
                report += $"📋 تم العثور على {templates.Count} قالب\n\n";

                if (!templates.Any())
                {
                    report += "❌ لا توجد قوالب للتعديل\n";
                    return report;
                }

                int modifiedCount = 0;

                foreach (var template in templates)
                {
                    if (!string.IsNullOrEmpty(template.PriceTemplate))
                    {
                        var originalText = template.PriceTemplate;
                        
                        // حذف المبلغ الثابت من الأقواس فقط
                        var modifiedText = template.PriceTemplate
                            .Replace("( 35200.00 )", "()")
                            .Replace("(35200.00)", "()")
                            .Replace("( 35,200.00 )", "()")
                            .Replace("(35,200.00)", "()")
                            .Replace("( 35200 )", "()")
                            .Replace("(35200)", "()")
                            .Replace("( 35,200 )", "()")
                            .Replace("(35,200)", "()");

                        if (originalText != modifiedText)
                        {
                            template.PriceTemplate = modifiedText;
                            template.LastModified = DateTime.Now;
                            modifiedCount++;
                            
                            report += $"✅ تم تعديل القالب {template.Id}\n";
                            report += $"   📝 قبل: {originalText.Substring(0, Math.Min(60, originalText.Length))}...\n";
                            report += $"   📝 بعد: {modifiedText.Substring(0, Math.Min(60, modifiedText.Length))}...\n\n";
                        }
                    }
                }

                if (modifiedCount > 0)
                {
                    await context.SaveChangesAsync();
                    report += $"💾 تم حفظ التغييرات - عدد القوالب المعدلة: {modifiedCount}\n";
                }
                else
                {
                    report += "ℹ️ لم يتم العثور على مبالغ ثابتة للحذف\n";
                }

                // التحقق النهائي
                var finalTemplates = await context.ContractTemplates.ToListAsync();
                report += "\n🔍 التحقق النهائي:\n";
                
                foreach (var template in finalTemplates)
                {
                    if (template.PriceTemplate?.Contains("35200") == true)
                    {
                        report += $"⚠️ القالب {template.Id} ما زال يحتوي على مبلغ ثابت\n";
                    }
                    else if (template.PriceTemplate?.Contains("()") == true)
                    {
                        report += $"✅ القالب {template.Id} يحتوي على أقواس فارغة\n";
                    }
                }

                report += "\n🎉 تمت العملية بنجاح!\n";
                report += "💡 الآن الأقواس فارغة ويمكن ملؤها بالمبلغ الديناميكي\n";

                return report;
            }
            catch (Exception ex)
            {
                return $"❌ خطأ في حذف المبلغ الثابت: {ex.Message}";
            }
        }

        /// <summary>
        /// استبدال الأقواس الفارغة بمتغير ديناميكي
        /// </summary>
        public static async Task<string> ReplaceEmptyBracketsWithVariableAsync()
        {
            try
            {
                using var context = new ApplicationDbContext();
                var report = "🔄 استبدال الأقواس الفارغة بمتغير ديناميكي\n";
                report += "=====================================\n\n";

                var templates = await context.ContractTemplates.ToListAsync();
                int modifiedCount = 0;

                foreach (var template in templates)
                {
                    if (!string.IsNullOrEmpty(template.PriceTemplate) && template.PriceTemplate.Contains("()"))
                    {
                        var originalText = template.PriceTemplate;
                        
                        // استبدال الأقواس الفارغة بالمتغير
                        template.PriceTemplate = template.PriceTemplate.Replace("()", "({TotalPrice})");
                        template.LastModified = DateTime.Now;
                        modifiedCount++;
                        
                        report += $"✅ تم تعديل القالب {template.Id}\n";
                        report += $"   📝 قبل: ...{originalText.Substring(Math.Max(0, originalText.IndexOf("()") - 20), Math.Min(40, originalText.Length - Math.Max(0, originalText.IndexOf("()") - 20)))}...\n";
                        report += $"   📝 بعد: ...{template.PriceTemplate.Substring(Math.Max(0, template.PriceTemplate.IndexOf("({TotalPrice})") - 20), Math.Min(40, template.PriceTemplate.Length - Math.Max(0, template.PriceTemplate.IndexOf("({TotalPrice})") - 20)))}...\n\n";
                    }
                }

                if (modifiedCount > 0)
                {
                    await context.SaveChangesAsync();
                    report += $"💾 تم حفظ التغييرات - عدد القوالب المعدلة: {modifiedCount}\n";
                }
                else
                {
                    report += "ℹ️ لم يتم العثور على أقواس فارغة للاستبدال\n";
                }

                report += "\n🎉 تمت العملية بنجاح!\n";
                report += "💡 الآن المتغير {TotalPrice} سيعرض المبلغ الصحيح\n";

                return report;
            }
            catch (Exception ex)
            {
                return $"❌ خطأ في استبدال الأقواس الفارغة: {ex.Message}";
            }
        }

        /// <summary>
        /// عملية شاملة: حذف المبلغ الثابت واستبداله بمتغير
        /// </summary>
        public static async Task<string> CompleteFixAsync()
        {
            try
            {
                var report = "🔧 بدء العملية الشاملة لإصلاح المبلغ\n";
                report += "=====================================\n\n";

                // الخطوة 1: حذف المبلغ الثابت
                report += "الخطوة 1: حذف المبلغ الثابت من الأقواس\n";
                report += "-----------------------------------\n";
                var step1Result = await RemoveFixedAmountFromBracketsAsync();
                report += step1Result + "\n\n";

                // الخطوة 2: استبدال الأقواس الفارغة بمتغير
                report += "الخطوة 2: استبدال الأقواس الفارغة بمتغير ديناميكي\n";
                report += "--------------------------------------------\n";
                var step2Result = await ReplaceEmptyBracketsWithVariableAsync();
                report += step2Result + "\n\n";

                report += "🎉 تمت العملية الشاملة بنجاح!\n";
                report += "💡 الآن العقود ستعرض المبالغ الصحيحة بدلاً من 35200.00\n";

                return report;
            }
            catch (Exception ex)
            {
                return $"❌ خطأ في العملية الشاملة: {ex.Message}";
            }
        }
    }
}
