# دليل محوِّل تنسيق الأسعار مع رقمين عشريّين

## 📋 **نظرة عامة**

تم تطبيق نظام شامل لتنسيق الأسعار مع عرض رقمين عشريّين دائمًا في جميع أنحاء النظام.

## 🛠️ **المكونات المطبقة**

### 1️⃣ **PriceFormatterConverter**
```csharp
// الموقع: Converters/ValueConverters.cs
public class PriceFormatterConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value == null) return string.Empty;

        if (value is IFormattable formattable)
        {
            return formattable.ToString("#,##0.00", CultureInfo.InvariantCulture);
        }

        if (decimal.TryParse(value.ToString(), out decimal decimalValue))
        {
            return decimalValue.ToString("#,##0.00", CultureInfo.InvariantCulture);
        }

        return value.ToString();
    }
}
```

### 2️⃣ **FormatCurrency المحدثة**
```csharp
// الموقع: ViewModels/ReportViewModel.cs
private string FormatCurrency(decimal amount)
{
    try
    {
        return amount.ToString("#,##0.00", System.Globalization.CultureInfo.InvariantCulture);
    }
    catch
    {
        return amount.ToString();
    }
}
```

### 3️⃣ **FormatNumberWithCommas المحدثة**
```csharp
// الموقع: Helpers/NumberToArabicTextHelper.cs
public static string FormatNumberWithCommas(decimal number)
{
    try
    {
        return number.ToString("#,##0.00", System.Globalization.CultureInfo.InvariantCulture);
    }
    catch
    {
        return number.ToString("#,##0.00", System.Globalization.CultureInfo.InvariantCulture);
    }
}
```

### 4️⃣ **خاصية FormattedWinningPrice**
```csharp
// الموقع: ViewModels/ReportViewModel.cs
public string FormattedWinningPrice
{
    get
    {
        if (ReportData?.WinnerDriver?.WinningPrice > 0)
        {
            return Helpers.NumberToArabicTextHelper.FormatNumberWithCommas(ReportData.WinnerDriver.WinningPrice);
        }
        return "0.00";
    }
}
```

## 🎯 **طرق الاستخدام**

### **الطريقة الأولى: المحوِّل في XAML**
```xml
<UserControl.Resources>
    <conv:PriceFormatterConverter x:Key="PriceFormatter"/>
</UserControl.Resources>

<TextBlock>
    <Run Text="مبلغ وقدره (" />
    <Run Text="{Binding ReportData.WinnerDriver.WinningPrice, Converter={StaticResource PriceFormatter}}" 
         FontWeight="Bold"/>
    <Run Text=") ريال يمني" />
</TextBlock>
```

### **الطريقة الثانية: خاصية منسقة**
```xml
<TextBlock>
    <Run Text="مبلغ وقدره (" />
    <Run Text="{Binding FormattedWinningPrice}" FontWeight="Bold"/>
    <Run Text=") ريال يمني" />
</TextBlock>
```

### **في جداول البيانات**
```xml
<DataGrid ItemsSource="{Binding ReportData.PriceOffers}">
    <DataGrid.Columns>
        <DataGridTextColumn Header="السعر المقترح">
            <DataGridTextColumn.Binding>
                <Binding Path="OfferedPrice" Converter="{StaticResource PriceFormatter}"/>
            </DataGridTextColumn.Binding>
        </DataGridTextColumn>
    </DataGrid.Columns>
</DataGrid>
```

## 📊 **أمثلة على النتائج**

| القيمة الأصلية | النتيجة المنسقة |
|----------------|-----------------|
| 35200          | 35,200.00       |
| 50000          | 50,000.00       |
| 123456.78      | 123,456.78      |
| 0              | 0.00            |
| 1234567890     | 1,234,567,890.00|

## ✅ **المزايا**

1. **تنسيق موحد** في جميع أنحاء النظام
2. **رقمين عشريّين دائمًا** للوضوح
3. **فواصل الآلاف** لسهولة القراءة
4. **مرونة في الاستخدام** (محوِّل أو خاصية)
5. **معالجة الأخطاء** المدمجة

## 🔧 **التحديث التلقائي**

عند تحديث `WinnerDriver.WinningPrice`، يتم تحديث `FormattedWinningPrice` تلقائيًا:

```csharp
// تحديث البيانات في النموذج
ReportData.WinnerDriver = winnerDriver;

// تحديث النص العربي للمبلغ
ReportData.UpdateWinnerPriceText();

// تحديث المبلغ المنسق
RaisePropertyChanged(nameof(FormattedWinningPrice));
```

## 🎯 **النتيجة النهائية**

الآن جميع الأسعار في النظام تظهر بتنسيق موحد مع رقمين عشريّين دائمًا:
- **في العقود**: `(35,200.00) ريال يمني`
- **في التقارير**: `35,200.00`
- **في جداول البيانات**: `35,200.00`
- **في الرسائل**: `35,200.00`
