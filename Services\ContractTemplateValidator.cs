using System;
using System.Threading.Tasks;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة التحقق من صحة قوالب العقود قبل الاستخدام
    /// </summary>
    public static class ContractTemplateValidator
    {
        /// <summary>
        /// التحقق من صحة قالب العقد وإصلاحه إذا لزم الأمر
        /// </summary>
        public static async Task<ContractTemplate> ValidateAndFixTemplateAsync(ApplicationDbContext context)
        {
            try
            {
                var template = await context.ContractTemplates.FirstOrDefaultAsync();

                if (template == null)
                {
                    // إنشاء قالب جديد إذا لم يوجد
                    template = await CreateNewTemplateAsync(context);
                }
                else
                {
                    // التحقق من صحة القالب الموجود
                    if (IsTemplateCorrupted(template))
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ تم اكتشاف قالب عقد تالف - جاري الإصلاح...");
                        template = await FixCorruptedTemplateAsync(context, template);
                    }
                }

                return template;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من قالب العقد: {ex.Message}");
                // إنشاء قالب جديد في حالة الخطأ
                return await CreateNewTemplateAsync(context);
            }
        }

        /// <summary>
        /// التحقق من وجود فساد في القالب
        /// </summary>
        private static bool IsTemplateCorrupted(ContractTemplate template)
        {
            if (string.IsNullOrEmpty(template.PriceTemplate))
                return true;

            if (!template.PriceTemplate.Contains("{TotalPrice}"))
                return true;

            if (template.PriceTemplate.Contains("35200") || 
                template.PriceTemplate.Contains("35,200") ||
                template.PriceTemplate.Contains(".00"))
                return true;

            return false;
        }

        /// <summary>
        /// إصلاح القالب التالف
        /// </summary>
        private static async Task<ContractTemplate> FixCorruptedTemplateAsync(ApplicationDbContext context, ContractTemplate template)
        {
            try
            {
                // إصلاح النص
                template.PriceTemplate = template.PriceTemplate
                    .Replace("35200.00", "{TotalPrice}")
                    .Replace("35,200.00", "{TotalPrice}")
                    .Replace("35200", "{TotalPrice}")
                    .Replace("35,200", "{TotalPrice}")
                    .Replace("{TotalPrice}.00", "{TotalPrice}")
                    .Replace("( 35200.00 )", "({TotalPrice})")
                    .Replace("(35200.00)", "({TotalPrice})")
                    .Replace("( 35,200.00 )", "({TotalPrice})")
                    .Replace("(35,200.00)", "({TotalPrice})");

                // إذا لم يحتوي على {TotalPrice} أصلاً، استبدل أي رقم كبير
                if (!template.PriceTemplate.Contains("{TotalPrice}"))
                {
                    template.PriceTemplate = System.Text.RegularExpressions.Regex.Replace(
                        template.PriceTemplate, 
                        @"\b\d{5,}\b", 
                        "{TotalPrice}");
                }

                // التأكد من وجود الأقواس حول المبلغ
                if (template.PriceTemplate.Contains("{TotalPrice}") && !template.PriceTemplate.Contains("({TotalPrice})"))
                {
                    template.PriceTemplate = template.PriceTemplate.Replace("{TotalPrice}", "({TotalPrice})");
                }

                // إذا لم ينجح الإصلاح، استخدم النص الافتراضي
                if (IsTemplateCorrupted(template))
                {
                    template.PriceTemplate = "اتفق الطرفان على أن القيمة الإيجارية لوسيلة النقل خلال فترة تنفيذ المهمة بإكملها مبلغ وقدره ({TotalPrice}) ريال يمني، وتدفع من الطرف الثاني للطرف الأول وفقاً للإجراءات المالية للصندوق وتصرف بحسب عدد الأيام الفعلية المنجزة.";
                }

                template.LastModified = DateTime.Now;
                await context.SaveChangesAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم إصلاح القالب التالف");
                return template;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل في إصلاح القالب: {ex.Message}");
                return await CreateNewTemplateAsync(context);
            }
        }

        /// <summary>
        /// إنشاء قالب جديد صحيح
        /// </summary>
        private static async Task<ContractTemplate> CreateNewTemplateAsync(ApplicationDbContext context)
        {
            try
            {
                var newTemplate = new ContractTemplate
                {
                    ContractIntroduction = "أنه في يوم {ContractDateArabic} الموافق {ContractDate} م بأمانة العاصمة صنعاء، تم إبرام هذا العقد بين كل من:",
                    
                    FirstPartyTemplate = "الاسم: {DriverName}، رقم البطاقة الشخصية: {NationalId}، مكان الإصدار: {IssuePlace}، تاريخ الإصدار: {IssueDate}",
                    
                    SecondPartyTemplate = "الصندوق الاجتماعي للتنمية - المركز الرئيسي ومقره شارع الشهيد إبراهيم الحمدي (شارع الستين الجنوبي) مقابل الجهاز المركزي للرقابة والمحاسبة، ويمثله في هذا العقد الأستاذ عبدالله علي الديلمي بصفته المدير التنفيذي للصندوق.",
                    
                    VehicleSpecsTemplate = "أجر الطرف الأول للطرف الثاني سيارة بالمواصفات التالية: نوع السيارة: {VehicleType}، سنة الصنع: {ManufactureYear}، لون السيارة: {VehicleColor}، رقم اللوحة: {VehicleNumber}، رقم رخصة التسيير: {LicenseNumber}، تاريخ إصدار الرخصة: {LicenseIssueDate}",
                    
                    PurposeTemplate = "اتفق الطرفان على استئجار الطرف الثاني وسيلة نقل (سيارة مع سائقها) لمرافقة الأخ/{VisitConductor}، صفته {VisitConductorRank} أثناء تنفيذ المهام الميدانية المطلوبة منه.",
                    
                    DurationTemplate = "تاريخ بداية المهمة: {StartDateArabic} الموافق {StartDate}م، تاريخ انتهاء المهمة: {EndDateArabic} الموافق {EndDate}م، إجمالي عدد الأيام: {DaysCount} أيام. تسير المهمة بموجب وثيقة خطة تنفيذ المهمة المرفقة بالعقد.",
                    
                    // النص الصحيح للسعر
                    PriceTemplate = "اتفق الطرفان على أن القيمة الإيجارية لوسيلة النقل خلال فترة تنفيذ المهمة بإكملها مبلغ وقدره ({TotalPrice}) ريال يمني، وتدفع من الطرف الثاني للطرف الأول وفقاً للإجراءات المالية للصندوق وتصرف بحسب عدد الأيام الفعلية المنجزة.",
                    
                    OwnershipTemplate = "يقر الطرف الأول بموجب هذا العقد بأنه المالك الشرعي لوسيلة النقل، وأنه حائز على وثائق إثبات الملكية الشرعية سارية المفعول وأنها خالية من أي التزامات شخصية أو عينية أو مطالبات أو حقوق للغير.",
                    
                    ObligationsTemplate = "يلتزم الطرف الأول بموجب هذا العقد بأن ينفذ العقد بنفسه وأن يبذل في تأديته من العناية مايبذله الشخص المعتاد، والامتثال بأخلاقيات ومدونة سلوك الصندوق الاجتماعي للتنمية أثناء الرحلة، والحرص على سلامة الركاب والالتزام بكافة قواعد وآداب المرور وقانونه، وعدم نقل أي ركاب آخرين عدا الطرف الثاني أثناء أداء المهام الميدانية، واصطحاب كافة وثائق الملكية أثناء تنفيذ المهمة، والصيانة الدورية لوسيلة النقل وفحصها قبل تنفيذ المهمة، وسرعة الإبلاغ عن أي حوادث تحدث أثناء قيادة المركبة، واصطحاب أدوات السلامة مثل طفاية الحريق وصندوق إسعافات أولية ومثلث التحذير وعدة صيانة السيارة.",
                    
                    CreatedDate = DateTime.Now,
                    LastModified = DateTime.Now
                };

                context.ContractTemplates.Add(newTemplate);
                await context.SaveChangesAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قالب عقد جديد صحيح");
                return newTemplate;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل في إنشاء قالب جديد: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// التحقق السريع من صحة النص
        /// </summary>
        public static string ValidatePriceText(string priceText, decimal totalPrice)
        {
            if (string.IsNullOrEmpty(priceText) || !priceText.Contains("{TotalPrice}"))
            {
                return $"اتفق الطرفان على أن القيمة الإيجارية لوسيلة النقل خلال فترة تنفيذ المهمة بإكملها مبلغ وقدره ({totalPrice:N0}) ريال يمني، وتدفع من الطرف الثاني للطرف الأول وفقاً للإجراءات المالية للصندوق وتصرف بحسب عدد الأيام الفعلية المنجزة.";
            }

            return priceText.Replace("{TotalPrice}", $"{totalPrice:N0}");
        }
    }
}
