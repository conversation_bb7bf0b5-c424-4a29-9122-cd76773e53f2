// DriverOffer.cs – نسخة محدّثة مع تنسيق عشري (#,##0.00) ومراعاة تحديث الخصائص المنسَّقة
using System;
using System.ComponentModel;
using System.Globalization;
using System.Runtime.CompilerServices;

namespace DriverManagementSystem.Models
{
    /// <summary>
    /// نموذج عرض السعر للسائق – يُستخدم في نافذة العروض.
    ///</summary>
    public class DriverOffer : INotifyPropertyChanged
    {
        private static readonly CultureInfo Inv = CultureInfo.InvariantCulture;

        private bool _isSelected;
        private decimal _proposedAmount;
        private int _daysCount;
        private bool _isWinner;
        private string _offerStatus = "تم التقديم";

        #region بيانات أساسية
        public int DriverId { get; set; }
        public string DriverName { get; set; } = string.Empty;
        public string DriverCode { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string VehicleType { get; set; } = string.Empty;
        public string VehicleNumber { get; set; } = string.Empty;
        public string VisitNumber { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        #endregion

        #region خصائص قابلة للتحرير والحساب
        /// <summary>عدد أيام الزيارة.</summary>
        public int DaysCount
        {
            get => _daysCount;
            set
            {
                if (_daysCount != value)
                {
                    _daysCount = value;
                    OnPropertyChanged();
                    RaiseRateProperties();
                }
            }
        }

        /// <summary>المبلغ الكلي المقترح.</summary>
        public decimal ProposedAmount
        {
            get => _proposedAmount;
            set
            {
                if (_proposedAmount != value)
                {
                    _proposedAmount = value;
                    OnPropertyChanged();
                    RaiseAmountProperties();
                }
            }
        }

        /// <summary>المعدل اليومي (يُحسب تلقائيًا).</summary>
        public decimal DailyRate => DaysCount > 0 ? ProposedAmount / DaysCount : 0;
        #endregion

        #region فوز / اختيار / حالة
        public bool IsWinner
        {
            get => _isWinner;
            set
            {
                if (_isWinner != value)
                {
                    _isWinner = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(WinnerIndicator));

                    OfferStatus = value ? "🏆 فائز" : "تم التقديم"; // سيُحدّث Auto داخل setter
                }
            }
        }

        /// <summary>مؤشر (رمز) للفوز.</summary>
        public string WinnerIndicator => IsWinner ? "🏆" : string.Empty;

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        public string OfferStatus
        {
            get => _offerStatus;
            set
            {
                if (_offerStatus != value)
                {
                    _offerStatus = value;
                    OnPropertyChanged();

                    // مزامنة حالة الفوز إذا تغيّرت الحالة يدويًا
                    if (value == "🏆 فائز" && !_isWinner)
                    {
                        _isWinner = true;
                        OnPropertyChanged(nameof(IsWinner));
                        OnPropertyChanged(nameof(WinnerIndicator));
                    }
                    else if (value != "🏆 فائز" && _isWinner)
                    {
                        _isWinner = false;
                        OnPropertyChanged(nameof(IsWinner));
                        OnPropertyChanged(nameof(WinnerIndicator));
                    }
                }
            }
        }
        #endregion

        #region خصائص منسَّقة للعرض
        public string FormattedAmount => $"{ProposedAmount.ToString("#,##0.00", Inv)} ريال";
        public string FormattedDailyRate => $"{DailyRate.ToString("#,##0.00", Inv)} ريال/يوم";
        public string DriverInfo => $"{DriverName} ({DriverCode})";
        public string ContactInfo => PhoneNumber;
        public string VehicleInfo => string.IsNullOrWhiteSpace(VehicleType)
                                        ? "غير محدد"
                                        : $"{VehicleType} - {VehicleNumber}";
        #endregion

        #region INotifyPropertyChanged
        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string? propertyName = null)
            => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));

        private void RaiseAmountProperties()
        {
            OnPropertyChanged(nameof(FormattedAmount));
            RaiseRateProperties();
        }

        private void RaiseRateProperties()
        {
            OnPropertyChanged(nameof(DailyRate));
            OnPropertyChanged(nameof(FormattedDailyRate));
        }
        #endregion

        #region تحويلات ثابتة
        public static DriverOffer FromDriver(Driver driver, int daysCount = 1) => new()
        {
            DriverId = driver.Id,
            DriverName = driver.Name,
            DriverCode = driver.DriverCode,
            PhoneNumber = driver.PhoneNumber ?? string.Empty,
            VehicleType = driver.VehicleType ?? string.Empty,
            VehicleNumber = driver.VehicleNumber ?? string.Empty,
            DaysCount = daysCount,
            ProposedAmount = driver.QuotedPrice ?? 0,
            CreatedAt = DateTime.Now
        };

        public string ToSaveString() => $"{DriverName} - {FormattedAmount}";

        public DriverQuote ToDriverQuote() => new()
        {
            DriverId = DriverId,
            DriverName = DriverName,
            DriverCode = DriverCode,
            PhoneNumber = PhoneNumber,
            VehicleType = VehicleType,
            VehicleNumber = VehicleNumber,
            QuotedPrice = ProposedAmount,
            QuotedDays = DaysCount,
            QuoteDate = DateTime.Now,
            Status = IsSelected ? QuoteStatus.Accepted : QuoteStatus.Pending
        };
        #endregion

        #region صلاحية ونسخ
        public bool IsValid() => !string.IsNullOrWhiteSpace(DriverName) && ProposedAmount > 0 && DaysCount > 0;

        public void CopyFrom(DriverOffer other)
        {
            if (other == null) return;

            DriverId = other.DriverId;
            DriverName = other.DriverName;
            DriverCode = other.DriverCode;
            PhoneNumber = other.PhoneNumber;
            VehicleType = other.VehicleType;
            VehicleNumber = other.VehicleNumber;
            DaysCount = other.DaysCount;
            ProposedAmount = other.ProposedAmount;
            IsSelected = other.IsSelected;
            IsWinner = other.IsWinner;
        }
        #endregion

        public override string ToString() => $"{DriverName} - {FormattedAmount} ({FormattedDailyRate})";
        public override bool Equals(object? obj) => obj is DriverOffer o && DriverId == o.DriverId;
        public override int GetHashCode() => DriverId.GetHashCode();
    }
}
