using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;
using DriverManagementSystem.Helpers;
using System.Windows;

namespace DriverManagementSystem.ViewModels
{
    public class ReportViewModel : INotifyPropertyChanged
    {
        #region Fields
        private readonly DatabaseService _databaseService;
        private readonly DriverDataService _driverDataService;
        private readonly ApplicationDbContext _context;
        private ReportModel _reportData;
        private ObservableCollection<FieldVisit> _fieldVisits;
        private FieldVisit _selectedVisit;

        public ReportViewModel()
        {
            _databaseService = new DatabaseService();
            _driverDataService = new DriverDataService(_context);
            _context = new ApplicationDbContext();

            // Initialize commands - simplified
            LoadDataCommand = new RelayCommand(LoadData);
            PrintReportCommand = new RelayCommand(PrintReport);
            ExportToPdfCommand = new RelayCommand(ExportToPdf);

            LoadData();

            System.Diagnostics.Debug.WriteLine("🔧 تم إنشاء ReportViewModel جديد");
        }

        #endregion

        #region Properties

        public ReportModel ReportData
        {
            get => _reportData;
            set
            {
                _reportData = value;
                RaisePropertyChanged();
            }
        }

        public ObservableCollection<FieldVisit> FieldVisits
        {
            get => _fieldVisits;
            set
            {
                _fieldVisits = value;
                RaisePropertyChanged();
            }
        }

        public FieldVisit SelectedVisit
        {
            get => _selectedVisit;
            set
            {
                _selectedVisit = value;
                RaisePropertyChanged();
                if (value != null)
                {
                    LoadReportData(value);
                }
            }
        }

        #endregion

        #region Commands

        public ICommand LoadDataCommand { get; }
        public ICommand PrintReportCommand { get; }
        public ICommand ExportToPdfCommand { get; }

        #endregion

        #region Methods

        private void LoadData()
        {
            try
            {
                using var context = new ApplicationDbContext();
                var visits = context.FieldVisits.ToList();

                FieldVisits = new ObservableCollection<FieldVisit>(visits);

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {visits.Count} زيارة ميدانية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل البيانات: {ex.Message}");
            }
        }

        public void LoadReportData(FieldVisit visit)
        {
            try
            {
                if (visit == null) return;

                System.Diagnostics.Debug.WriteLine($"🎯 بدء تحميل بيانات التقرير للزيارة: {visit.VisitNumber}");

                // إنشاء نموذج التقرير مبسط
                ReportData = new ReportModel
                {
                    VisitNumber = visit.VisitNumber,
                    VisitConductor = "غير محدد"
                };

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل بيانات التقرير بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل بيانات التقرير: {ex.Message}");
            }
        }

        private void PrintReport()
        {
            try
            {
                if (ReportData == null)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد بيانات للطباعة");
                    return;
                }

                System.Diagnostics.Debug.WriteLine("🖨️ بدء عملية الطباعة");
                // TODO: تنفيذ منطق الطباعة
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطباعة: {ex.Message}");
            }
        }

        private bool CanPrintReport()
        {
            return ReportData != null;
        }

        private void ExportToPdf()
        {
            try
            {
                if (ReportData == null)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد بيانات للتصدير");
                    return;
                }

                System.Diagnostics.Debug.WriteLine("📄 بدء عملية التصدير إلى PDF");
                // TODO: تنفيذ منطق التصدير
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التصدير: {ex.Message}");
            }
        }

        private bool CanExportToPdf()
        {
            return ReportData != null;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
