using System;
using System.Threading.Tasks;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة الإصلاح التلقائي لقوالب العقود - تعمل عند بدء التطبيق
    /// </summary>
    public static class AutoContractTemplateFixService
    {
        /// <summary>
        /// إصلاح تلقائي لقوالب العقود عند بدء التطبيق
        /// </summary>
        public static async Task AutoFixContractTemplatesAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء الإصلاح التلقائي لقوالب العقود...");

                var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
                var connectionString = @"Server=(localdb)\MSSQLLocalDB;Database=DriverManagementDB;Trusted_Connection=true;MultipleActiveResultSets=true";
                optionsBuilder.UseSqlServer(connectionString);

                using var context = new ApplicationDbContext(optionsBuilder.Options);

                // التحقق من وجود مشكلة
                var needsFix = await CheckIfFixNeededAsync(context);

                if (needsFix)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ تم اكتشاف مشكلة في قوالب العقود - جاري الإصلاح...");
                    
                    // تطبيق الإصلاح
                    await ApplyFixAsync(context);
                    
                    System.Diagnostics.Debug.WriteLine("✅ تم إصلاح قوالب العقود تلقائياً");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("✅ قوالب العقود سليمة - لا حاجة للإصلاح");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الإصلاح التلقائي: {ex.Message}");
                // لا نرمي الخطأ لتجنب توقف التطبيق
            }
        }

        /// <summary>
        /// التحقق من الحاجة للإصلاح
        /// </summary>
        private static async Task<bool> CheckIfFixNeededAsync(ApplicationDbContext context)
        {
            try
            {
                var templates = await context.ContractTemplates.ToListAsync();

                foreach (var template in templates)
                {
                    if (string.IsNullOrEmpty(template.PriceTemplate) ||
                        !template.PriceTemplate.Contains("{TotalPrice}") ||
                        template.PriceTemplate.Contains("35200") ||
                        template.PriceTemplate.Contains("35,200"))
                    {
                        return true; // يحتاج إصلاح
                    }
                }

                return false; // لا يحتاج إصلاح
            }
            catch
            {
                return false; // في حالة الخطأ، لا نصلح
            }
        }

        /// <summary>
        /// تطبيق الإصلاح
        /// </summary>
        private static async Task ApplyFixAsync(ApplicationDbContext context)
        {
            try
            {
                // الطريقة الأولى: إصلاح النصوص الموجودة
                var templates = await context.ContractTemplates.ToListAsync();

                bool fixApplied = false;

                foreach (var template in templates)
                {
                    if (!string.IsNullOrEmpty(template.PriceTemplate))
                    {
                        var originalText = template.PriceTemplate;

                        // إزالة جميع المبالغ الثابتة المحتملة
                        template.PriceTemplate = template.PriceTemplate
                            .Replace("35200.00", "{TotalPrice}")
                            .Replace("35,200.00", "{TotalPrice}")
                            .Replace("35200", "{TotalPrice}")
                            .Replace("35,200", "{TotalPrice}")
                            .Replace("{TotalPrice}.00", "{TotalPrice}")
                            .Replace("( 35200.00 )", "({TotalPrice})")
                            .Replace("(35200.00)", "({TotalPrice})")
                            .Replace("( 35,200.00 )", "({TotalPrice})")
                            .Replace("(35,200.00)", "({TotalPrice})");

                        // إذا لم يحتوي على {TotalPrice} أصلاً، استبدل أي رقم كبير
                        if (!template.PriceTemplate.Contains("{TotalPrice}"))
                        {
                            template.PriceTemplate = System.Text.RegularExpressions.Regex.Replace(
                                template.PriceTemplate, 
                                @"\b\d{5,}\b", 
                                "{TotalPrice}");
                        }

                        // التأكد من وجود الأقواس حول المبلغ
                        if (template.PriceTemplate.Contains("{TotalPrice}") && !template.PriceTemplate.Contains("({TotalPrice})"))
                        {
                            template.PriceTemplate = template.PriceTemplate.Replace("{TotalPrice}", "({TotalPrice})");
                        }

                        if (originalText != template.PriceTemplate)
                        {
                            template.LastModified = DateTime.Now;
                            fixApplied = true;
                            System.Diagnostics.Debug.WriteLine($"🔄 تم إصلاح القالب رقم {template.Id}");
                        }
                    }
                }

                // إذا لم ينجح الإصلاح، استخدم الطريقة الجذرية
                if (!fixApplied || !await CheckIfFixNeededAsync(context))
                {
                    await ApplyRadicalFixAsync(context);
                }
                else
                {
                    await context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق الإصلاح: {ex.Message}");
                // جرب الطريقة الجذرية
                await ApplyRadicalFixAsync(context);
            }
        }

        /// <summary>
        /// الإصلاح الجذري - حذف وإعادة إنشاء
        /// </summary>
        private static async Task ApplyRadicalFixAsync(ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🗑️ تطبيق الإصلاح الجذري...");

                // حذف جميع القوالب الموجودة
                var existingTemplates = await context.ContractTemplates.ToListAsync();
                context.ContractTemplates.RemoveRange(existingTemplates);
                await context.SaveChangesAsync();

                // إنشاء قالب جديد صحيح
                var newTemplate = new ContractTemplate
                {
                    ContractIntroduction = "أنه في يوم {ContractDateArabic} الموافق {ContractDate} م بأمانة العاصمة صنعاء، تم إبرام هذا العقد بين كل من:",
                    
                    FirstPartyTemplate = "الاسم: {DriverName}، رقم البطاقة الشخصية: {NationalId}، مكان الإصدار: {IssuePlace}، تاريخ الإصدار: {IssueDate}",
                    
                    SecondPartyTemplate = "الصندوق الاجتماعي للتنمية - المركز الرئيسي ومقره شارع الشهيد إبراهيم الحمدي (شارع الستين الجنوبي) مقابل الجهاز المركزي للرقابة والمحاسبة، ويمثله في هذا العقد الأستاذ عبدالله علي الديلمي بصفته المدير التنفيذي للصندوق.",
                    
                    VehicleSpecsTemplate = "أجر الطرف الأول للطرف الثاني سيارة بالمواصفات التالية: نوع السيارة: {VehicleType}، سنة الصنع: {ManufactureYear}، لون السيارة: {VehicleColor}، رقم اللوحة: {VehicleNumber}، رقم رخصة التسيير: {LicenseNumber}، تاريخ إصدار الرخصة: {LicenseIssueDate}",
                    
                    PurposeTemplate = "اتفق الطرفان على استئجار الطرف الثاني وسيلة نقل (سيارة مع سائقها) لمرافقة الأخ/{VisitConductor}، صفته {VisitConductorRank} أثناء تنفيذ المهام الميدانية المطلوبة منه.",
                    
                    DurationTemplate = "تاريخ بداية المهمة: {StartDateArabic} الموافق {StartDate}م، تاريخ انتهاء المهمة: {EndDateArabic} الموافق {EndDate}م، إجمالي عدد الأيام: {DaysCount} أيام. تسير المهمة بموجب وثيقة خطة تنفيذ المهمة المرفقة بالعقد.",
                    
                    // النص الصحيح للسعر
                    PriceTemplate = "اتفق الطرفان على أن القيمة الإيجارية لوسيلة النقل خلال فترة تنفيذ المهمة بإكملها مبلغ وقدره ({TotalPrice}) ريال يمني، وتدفع من الطرف الثاني للطرف الأول وفقاً للإجراءات المالية للصندوق وتصرف بحسب عدد الأيام الفعلية المنجزة.",
                    
                    OwnershipTemplate = "يقر الطرف الأول بموجب هذا العقد بأنه المالك الشرعي لوسيلة النقل، وأنه حائز على وثائق إثبات الملكية الشرعية سارية المفعول وأنها خالية من أي التزامات شخصية أو عينية أو مطالبات أو حقوق للغير.",
                    
                    ObligationsTemplate = "يلتزم الطرف الأول بموجب هذا العقد بأن ينفذ العقد بنفسه وأن يبذل في تأديته من العناية مايبذله الشخص المعتاد، والامتثال بأخلاقيات ومدونة سلوك الصندوق الاجتماعي للتنمية أثناء الرحلة، والحرص على سلامة الركاب والالتزام بكافة قواعد وآداب المرور وقانونه، وعدم نقل أي ركاب آخرين عدا الطرف الثاني أثناء أداء المهام الميدانية، واصطحاب كافة وثائق الملكية أثناء تنفيذ المهمة، والصيانة الدورية لوسيلة النقل وفحصها قبل تنفيذ المهمة، وسرعة الإبلاغ عن أي حوادث تحدث أثناء قيادة المركبة، واصطحاب أدوات السلامة مثل طفاية الحريق وصندوق إسعافات أولية ومثلث التحذير وعدة صيانة السيارة.",
                    
                    CreatedDate = DateTime.Now,
                    LastModified = DateTime.Now
                };

                context.ContractTemplates.Add(newTemplate);
                await context.SaveChangesAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قالب جديد صحيح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الإصلاح الجذري: {ex.Message}");
            }
        }
    }
}
