using System;
using System.Linq;
using System.Threading.Tasks;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// تشخيص وإصلاح سريع لقوالب العقود ومشاكل المبالغ العشرية
    /// </summary>
    public static class QuickTemplateFix
    {
        /// <summary>
        /// تشخيص شامل وإصلاح فوري لقوالب العقود
        /// </summary>
        public static async Task<string> DiagnoseAndFixNowAsync()
        {
            try
            {
                var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
                var connectionString = @"Server=(localdb)\MSSQLLocalDB;Database=DriverManagementDB;Trusted_Connection=true;MultipleActiveResultSets=true";
                optionsBuilder.UseSqlServer(connectionString);

                using var context = new ApplicationDbContext(optionsBuilder.Options);

                var report = "🩺 تشخيص شامل لقوالب العقود والمبالغ العشرية\n";
                report += "================================================\n\n";

                // الخطوة 1: فحص قاعدة البيانات
                report += "🔍 الخطوة 1: فحص قاعدة البيانات\n";
                report += "--------------------------------\n";

                try
                {
                    await context.Database.OpenConnectionAsync();
                    report += "✅ الاتصال بقاعدة البيانات: نجح\n";
                    await context.Database.CloseConnectionAsync();
                }
                catch (Exception dbEx)
                {
                    report += $"❌ الاتصال بقاعدة البيانات: فشل - {dbEx.Message}\n";
                    return report;
                }

                // الخطوة 2: فحص جدول قوالب العقود
                report += "\n🔍 الخطوة 2: فحص جدول قوالب العقود\n";
                report += "------------------------------------\n";

                // جلب جميع القوالب
                var templates = await context.ContractTemplates.ToListAsync();
                report += $"📋 عدد القوالب الموجودة: {templates.Count}\n\n";

                if (templates.Count == 0)
                {
                    report += "❌ لا توجد قوالب! جاري إنشاء قالب صحيح...\n";
                    await CreateCorrectTemplateAsync(context);
                    report += "✅ تم إنشاء قالب صحيح جديد\n";
                }
                else
                {
                    // فحص كل قالب
                    bool foundProblem = false;
                    for (int i = 0; i < templates.Count; i++)
                    {
                        var template = templates[i];
                        report += $"📄 القالب {i + 1} (ID: {template.Id}):\n";

                        if (string.IsNullOrEmpty(template.PriceTemplate))
                        {
                            report += "   ❌ قالب السعر فارغ\n";
                            foundProblem = true;
                        }
                        else if (template.PriceTemplate.Contains("35200") || template.PriceTemplate.Contains("35,200") ||
                                template.PriceTemplate.Contains("الإجارية") || template.PriceTemplate.Contains("بأكملها"))
                        {
                            report += "   ❌ يحتوي على مبلغ ثابت أو نص قديم\n";
                            if (template.PriceTemplate.Contains("35200"))
                                report += "   📍 يحتوي على مبلغ ثابت: 35200\n";
                            if (template.PriceTemplate.Contains("الإجارية"))
                                report += "   📍 يحتوي على كلمة قديمة: الإجارية (يجب أن تكون: الإيجارية)\n";
                            if (template.PriceTemplate.Contains("بأكملها"))
                                report += "   📍 يحتوي على إملاء قديم: بأكملها (يجب أن تكون: بإكملها)\n";
                            report += $"   📝 النص: {template.PriceTemplate.Substring(0, Math.Min(50, template.PriceTemplate.Length))}...\n";
                            foundProblem = true;
                        }
                        else if (template.PriceTemplate.Contains("{TotalPrice}"))
                        {
                            report += "   ✅ يحتوي على {TotalPrice} - صحيح\n";
                        }
                        else
                        {
                            report += "   ⚠️ لا يحتوي على {TotalPrice}\n";
                            foundProblem = true;
                        }

                        report += "\n";
                    }

                    if (foundProblem)
                    {
                        report += "🔧 تم اكتشاف مشاكل! جاري الحذف والإنشاء من جديد...\n\n";

                        // حذف جميع القوالب القديمة فوراً (بدون محاولة إصلاح)
                        context.ContractTemplates.RemoveRange(templates);
                        await context.SaveChangesAsync();
                        report += "🗑️ تم حذف جميع القوالب القديمة نهائياً\n";

                        // إنشاء قالب صحيح جديد تماماً
                        await CreateCorrectTemplateAsync(context);
                        report += "✅ تم إنشاء قالب صحيح جديد من الصفر\n";
                    }
                    else
                    {
                        report += "✅ جميع القوالب صحيحة!\n";
                    }
                }

                // التحقق النهائي والفحص العميق
                var finalTemplates = await context.ContractTemplates.ToListAsync();
                report += $"\n📊 النتيجة النهائية: {finalTemplates.Count} قالب\n";

                if (finalTemplates.Count == 1)
                {
                    var finalTemplate = finalTemplates.First();

                    // فحص عميق للقالب الحالي
                    report += "\n🔬 فحص عميق للقالب الحالي:\n";
                    report += $"   📅 تاريخ الإنشاء: {finalTemplate.CreatedDate:yyyy-MM-dd HH:mm:ss}\n";
                    report += $"   📅 آخر تعديل: {finalTemplate.LastModified:yyyy-MM-dd HH:mm:ss}\n";

                    // فحص نص السعر بالتفصيل
                    var priceText = finalTemplate.PriceTemplate ?? "";
                    report += $"   📝 طول نص السعر: {priceText.Length} حرف\n";

                    if (priceText.Contains("35200"))
                    {
                        report += "   ❌ تحذير: ما زال يحتوي على 35200!\n";
                        var index = priceText.IndexOf("35200");
                        var context_text = priceText.Substring(Math.Max(0, index - 20), Math.Min(40, priceText.Length - Math.Max(0, index - 20)));
                        report += $"   📍 السياق: ...{context_text}...\n";
                    }
                    else
                    {
                        report += "   ✅ لا يحتوي على 35200\n";
                    }

                    if (priceText.Contains("{TotalPrice}"))
                    {
                        report += "   ✅ يحتوي على {TotalPrice}\n";
                        var index = priceText.IndexOf("{TotalPrice}");
                        var context_text = priceText.Substring(Math.Max(0, index - 20), Math.Min(40, priceText.Length - Math.Max(0, index - 20)));
                        report += $"   📍 السياق: ...{context_text}...\n";
                    }
                    else
                    {
                        report += "   ❌ لا يحتوي على {TotalPrice}\n";
                    }

                    // عرض النص الكامل للسعر
                    report += "\n📖 النص الكامل لقالب السعر:\n";
                    report += "```\n";
                    report += priceText + "\n";
                    report += "```\n";

                    if (finalTemplate.PriceTemplate.Contains("{TotalPrice}") &&
                        !finalTemplate.PriceTemplate.Contains("35200"))
                    {
                        report += "\n🎉 تم الإصلاح بنجاح! القالب صحيح الآن\n";
                    }
                    else
                    {
                        report += "\n⚠️ ما زالت هناك مشكلة في القالب\n";
                    }
                }

                return report;
            }
            catch (Exception ex)
            {
                return $"❌ خطأ في الفحص والإصلاح: {ex.Message}";
            }
        }

        /// <summary>
        /// إنشاء قالب صحيح مضمون
        /// </summary>
        private static async Task CreateCorrectTemplateAsync(ApplicationDbContext context)
        {
            var correctTemplate = new ContractTemplate
            {
                ContractIntroduction = "أنه في يوم {ContractDateArabic} الموافق {ContractDate} م بأمانة العاصمة صنعاء، تم إبرام هذا العقد بين كل من:",

                FirstPartyTemplate = "الطرف الأول: {DriverName}، رقم البطاقة الشخصية: {NationalId}، مكان الإصدار: {IssuePlace}، تاريخ الإصدار: {IssueDate}، رقم الهاتف: {PhoneNumber}",

                SecondPartyTemplate = "الطرف الثاني: الصندوق الاجتماعي للتنمية - المركز الرئيسي ومقره شارع الشهيد إبراهيم الحمدي (شارع الستين الجنوبي) مقابل الجهاز المركزي للرقابة والمحاسبة، ويمثله في هذا العقد الأستاذ عبدالله علي الديلمي بصفته المدير التنفيذي للصندوق.",

                VehicleSpecsTemplate = "أجر الطرف الأول للطرف الثاني سيارة بالمواصفات التالية: نوع السيارة: {VehicleType}، سنة الصنع: {ManufactureYear}، لون السيارة: {VehicleColor}، رقم اللوحة: {VehicleNumber}، رقم رخصة التسيير: {LicenseNumber}، تاريخ إصدار الرخصة: {LicenseIssueDate}",

                PurposeTemplate = "اتفق الطرفان على استئجار الطرف الثاني وسيلة نقل (سيارة مع سائقها) لمرافقة الأخ/{VisitConductor}، صفته {VisitConductorRank} أثناء تنفيذ المهام الميدانية المطلوبة منه.",

                DurationTemplate = "تاريخ بداية المهمة: {StartDateArabic} الموافق {StartDate}م، تاريخ انتهاء المهمة: {EndDateArabic} الموافق {EndDate}م، إجمالي عدد الأيام: {DaysCount} أيام. تسير المهمة بموجب وثيقة خطة تنفيذ المهمة المرفقة بالعقد.",

                // النص الصحيح الوحيد للسعر - مكتوب من جديد تماماً
                PriceTemplate = "اتفق الطرفان على أن القيمة الإيجارية لوسيلة النقل خلال فترة تنفيذ المهمة بإكملها مبلغ وقدره ({TotalPrice}) ريال يمني، وتدفع من الطرف الثاني للطرف الأول وفقاً للإجراءات المالية للصندوق وتصرف بحسب عدد الأيام الفعلية المنجزة.",

                OwnershipTemplate = "يقر الطرف الأول بموجب هذا العقد بأنه المالك الشرعي لوسيلة النقل، وأنه حائز على وثائق إثبات الملكية الشرعية سارية المفعول وأنها خالية من أي التزامات شخصية أو عينية أو مطالبات أو حقوق للغير.",

                ObligationsTemplate = "يلتزم الطرف الأول بموجب هذا العقد بأن ينفذ العقد بنفسه وأن يبذل في تأديته من العناية مايبذله الشخص المعتاد، والامتثال بأخلاقيات ومدونة سلوك الصندوق الاجتماعي للتنمية أثناء الرحلة، والحرص على سلامة الركاب والالتزام بكافة قواعد وآداب المرور وقانونه، وعدم نقل أي ركاب آخرين عدا الطرف الثاني أثناء أداء المهام الميدانية، واصطحاب كافة وثائق الملكية أثناء تنفيذ المهمة، والصيانة الدورية لوسيلة النقل وفحصها قبل تنفيذ المهمة، وسرعة الإبلاغ عن أي حوادث تحدث أثناء قيادة المركبة، واصطحاب أدوات السلامة مثل طفاية الحريق وصندوق إسعافات أولية ومثلث التحذير وعدة صيانة السيارة.",

                CreatedDate = DateTime.Now,
                LastModified = DateTime.Now
            };

            context.ContractTemplates.Add(correctTemplate);
            await context.SaveChangesAsync();

            System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قالب صحيح مضمون");
            System.Diagnostics.Debug.WriteLine($"📝 نص السعر: {correctTemplate.PriceTemplate}");
        }

        /// <summary>
        /// إصلاح فوري بدون تشخيص
        /// </summary>
        public static async Task ForceFixNowAsync()
        {
            try
            {
                var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
                var connectionString = @"Server=(localdb)\MSSQLLocalDB;Database=DriverManagementDB;Trusted_Connection=true;MultipleActiveResultSets=true";
                optionsBuilder.UseSqlServer(connectionString);

                using var context = new ApplicationDbContext(optionsBuilder.Options);

                // حذف جميع القوالب
                var allTemplates = await context.ContractTemplates.ToListAsync();
                if (allTemplates.Any())
                {
                    context.ContractTemplates.RemoveRange(allTemplates);
                    await context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف {allTemplates.Count} قالب");
                }

                // إنشاء قالب صحيح جديد
                await CreateCorrectTemplateAsync(context);

                System.Diagnostics.Debug.WriteLine("🎉 تم الإصلاح الفوري بنجاح!");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الإصلاح الفوري: {ex.Message}");
                throw;
            }
        }
    }
}
