using System;

namespace DriverManagementSystem.Helpers
{
    /// <summary>
    /// مساعد تحويل الأرقام إلى نص عربي احترافي
    /// </summary>
    public static class NumberToArabicTextHelper
    {
        private static readonly string[] ones = {
            "", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة",
            "عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر", "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر"
        };

        private static readonly string[] tens = {
            "", "", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون"
        };

        private static readonly string[] hundreds = {
            "", "مائة", "مائتان", "ثلاثمائة", "أربعمائة", "خمسمائة", "ستمائة", "سبعمائة", "ثمانمائة", "تسعمائة"
        };

        /// <summary>
        /// تحويل رقم إلى نص عربي احترافي
        /// </summary>
        /// <param name="number">الرقم المراد تحويله</param>
        /// <returns>النص العربي للرقم</returns>
        public static string ConvertToArabicText(decimal number)
        {
            try
            {
                if (number == 0)
                    return "صفر";

                if (number < 0)
                    return "سالب " + ConvertToArabicText(-number);

                // تحويل إلى عدد صحيح (تجاهل الكسور)
                long intNumber = (long)number;

                if (intNumber == 0)
                    return "صفر";

                string result = "";

                // المليارات
                if (intNumber >= 1000000000)
                {
                    long billions = intNumber / 1000000000;
                    string billionText = ConvertHundreds((int)billions);
                    if (!string.IsNullOrEmpty(billionText))
                    {
                        result += billionText + " مليار";
                        intNumber %= 1000000000;
                        if (intNumber > 0) result += " و";
                    }
                }

                // الملايين
                if (intNumber >= 1000000)
                {
                    long millions = intNumber / 1000000;
                    string millionText = ConvertHundreds((int)millions);
                    if (!string.IsNullOrEmpty(millionText))
                    {
                        result += millionText + " مليون";
                        intNumber %= 1000000;
                        if (intNumber > 0) result += " و";
                    }
                }

                // الآلاف
                if (intNumber >= 1000)
                {
                    long thousands = intNumber / 1000;
                    string thousandText = ConvertHundreds((int)thousands);
                    if (!string.IsNullOrEmpty(thousandText))
                    {
                        if (thousands == 1)
                            result += "ألف";
                        else if (thousands == 2)
                            result += "ألفان";
                        else if (thousands <= 10)
                            result += thousandText + " آلاف";
                        else
                            result += thousandText + " ألف";

                        intNumber %= 1000;
                        if (intNumber > 0) result += " و";
                    }
                }

                // المئات والعشرات والآحاد
                if (intNumber > 0)
                {
                    string remainderText = ConvertHundreds((int)intNumber);
                    if (!string.IsNullOrEmpty(remainderText))
                    {
                        result += remainderText;
                    }
                }

                return result.Trim();
            }
            catch
            {
                return "خطأ في التحويل";
            }
        }

        /// <summary>
        /// تحويل الأرقام من 1 إلى 999 بطريقة احترافية
        /// </summary>
        private static string ConvertHundreds(int number)
        {
            if (number == 0) return "";

            string result = "";

            // المئات
            if (number >= 100)
            {
                int hundredsDigit = number / 100;
                result += hundreds[hundredsDigit];
                number %= 100;

                // إضافة "و" إذا كان هناك باقي
                if (number > 0)
                    result += " و";
            }

            // العشرات والآحاد
            if (number >= 20)
            {
                int tensDigit = number / 10;
                int onesDigit = number % 10;

                if (onesDigit > 0)
                    result += ones[onesDigit] + " و" + tens[tensDigit];
                else
                    result += tens[tensDigit];
            }
            else if (number > 0)
            {
                result += ones[number];
            }

            return result.Trim();
        }

        /// <summary>
        /// تحويل مبلغ مالي إلى نص عربي مع إضافة "ريال يمني"
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>النص العربي للمبلغ</returns>
        public static string ConvertAmountToArabicText(decimal amount)
        {
            try
            {
                if (amount == 0)
                    return "صفر ريال يمني";

                string arabicText = ConvertToArabicText(amount);

                // إضافة "ريال يمني" في النهاية
                if (amount == 1)
                    return arabicText + " ريال يمني";
                else if (amount == 2)
                    return arabicText + " ريال يمني";
                else
                    return arabicText + " ريال يمني";
            }
            catch
            {
                return "خطأ في تحويل المبلغ";
            }
        }

        /// <summary>
        /// تنسيق الأرقام بالفواصل للقراءة الأفضل
        /// </summary>
        /// <param name="number">الرقم المراد تنسيقه</param>
        /// <returns>الرقم مع الفواصل</returns>
        public static string FormatNumberWithCommas(decimal number)
        {
            try
            {
                return number.ToString("#,##0", System.Globalization.CultureInfo.InvariantCulture);
            }
            catch
            {
                return number.ToString("#,##0", System.Globalization.CultureInfo.InvariantCulture);
            }
        }

        public static string FormatNumberWithCommas(int number)
        {
            try
            {
                return number.ToString("#,##0", System.Globalization.CultureInfo.InvariantCulture);
            }
            catch
            {
                return number.ToString("#,##0", System.Globalization.CultureInfo.InvariantCulture);
            }
        }

        /// <summary>
        /// تحويل الأرقام إلى نص عربي (الدالة الرئيسية)
        /// </summary>
        /// <param name="number">الرقم</param>
        /// <returns>النص العربي</returns>
        public static string ConvertSimple(decimal number)
        {
            return ConvertToArabicText(number);
        }

    }
}
