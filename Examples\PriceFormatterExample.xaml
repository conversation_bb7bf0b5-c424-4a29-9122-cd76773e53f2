<!-- مثال على استخدام محوِّل تنسيق الأسعار مع رقمين عشريّين -->
<UserControl x:Class="DriverManagementSystem.Examples.PriceFormatterExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:conv="clr-namespace:DriverManagementSystem.Converters">

    <UserControl.Resources>
        <!-- تسجيل المحوِّل -->
        <conv:PriceFormatterConverter x:Key="PriceFormatter"/>
    </UserControl.Resources>

    <StackPanel Margin="20">
        <TextBlock Text="أمثلة على تنسيق الأسعار:" FontSize="16" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- الطريقة الأولى: استخدام المحوِّل في XAML -->
        <Border Background="#F0F8FF" Padding="15" Margin="0,0,0,10" CornerRadius="5">
            <StackPanel>
                <TextBlock Text="الطريقة الأولى: استخدام المحوِّل في XAML" FontWeight="Bold" Margin="0,0,0,10"/>
                <TextBlock>
                    <Run Text="مبلغ وقدره (" />
                    <Run Text="{Binding ReportData.WinnerDriver.WinningPrice, Converter={StaticResource PriceFormatter}}" 
                         FontWeight="Bold" Foreground="Blue"/>
                    <Run Text=") ريال يمني" />
                </TextBlock>
            </StackPanel>
        </Border>

        <!-- الطريقة الثانية: استخدام خاصية منسقة من ViewModel -->
        <Border Background="#F0FFF0" Padding="15" Margin="0,0,0,10" CornerRadius="5">
            <StackPanel>
                <TextBlock Text="الطريقة الثانية: استخدام خاصية منسقة من ViewModel" FontWeight="Bold" Margin="0,0,0,10"/>
                <TextBlock>
                    <Run Text="مبلغ وقدره (" />
                    <Run Text="{Binding FormattedWinningPrice}" 
                         FontWeight="Bold" Foreground="Green"/>
                    <Run Text=") ريال يمني" />
                </TextBlock>
            </StackPanel>
        </Border>

        <!-- مثال في جدول البيانات -->
        <Border Background="#FFF8DC" Padding="15" Margin="0,0,0,10" CornerRadius="5">
            <StackPanel>
                <TextBlock Text="مثال في جدول البيانات:" FontWeight="Bold" Margin="0,0,0,10"/>
                <DataGrid ItemsSource="{Binding ReportData.PriceOffers}" AutoGenerateColumns="False" Height="150">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم السائق" Binding="{Binding DriverName}" Width="150"/>
                        <DataGridTextColumn Header="السعر المقترح" Width="120">
                            <DataGridTextColumn.Binding>
                                <Binding Path="OfferedPrice" Converter="{StaticResource PriceFormatter}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </StackPanel>
        </Border>

        <!-- أمثلة على القيم المختلفة -->
        <Border Background="#FFE4E1" Padding="15" CornerRadius="5">
            <StackPanel>
                <TextBlock Text="أمثلة على القيم المختلفة:" FontWeight="Bold" Margin="0,0,0,10"/>
                <StackPanel Orientation="Horizontal" Margin="0,5">
                    <TextBlock Text="35200 ← " Width="80"/>
                    <TextBlock Text="35,200.00" FontWeight="Bold" Foreground="Red"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,5">
                    <TextBlock Text="50000 ← " Width="80"/>
                    <TextBlock Text="50,000.00" FontWeight="Bold" Foreground="Red"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,5">
                    <TextBlock Text="123456.78 ← " Width="80"/>
                    <TextBlock Text="123,456.78" FontWeight="Bold" Foreground="Red"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,5">
                    <TextBlock Text="0 ← " Width="80"/>
                    <TextBlock Text="0.00" FontWeight="Bold" Foreground="Red"/>
                </StackPanel>
            </StackPanel>
        </Border>
    </StackPanel>
</UserControl>
