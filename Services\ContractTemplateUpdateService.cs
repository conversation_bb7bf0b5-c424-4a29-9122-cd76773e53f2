using System;
using System.Threading.Tasks;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة تحديث قوالب العقود - حل جذري لمشكلة تنسيق المبالغ
    /// </summary>
    public class ContractTemplateUpdateService
    {
        private readonly ApplicationDbContext _context;

        public ContractTemplateUpdateService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// تحديث جميع قوالب العقود لضمان التنسيق الصحيح للمبالغ
        /// </summary>
        public async Task UpdateContractTemplatesAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء تحديث قوالب العقود...");

                // جلب أو إنشاء القالب الافتراضي
                var template = await _context.ContractTemplates.FirstOrDefaultAsync();
                
                if (template == null)
                {
                    // إنشاء قالب جديد
                    template = new ContractTemplate();
                    _context.ContractTemplates.Add(template);
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قالب عقد جديد");
                }

                // تحديث قالب السعر مع التأكد من التنسيق الصحيح
                template.PriceTemplate = "اتفق الطرفان على أن القيمة الإيجارية لوسيلة النقل خلال فترة تنفيذ المهمة بإكملها مبلغ وقدره ({TotalPrice}) ريال يمني، وتدفع من الطرف الثاني للطرف الأول وفقاً للإجراءات المالية للصندوق وتصرف بحسب عدد الأيام الفعلية المنجزة.";

                // تحديث باقي القوالب إذا لزم الأمر
                if (string.IsNullOrEmpty(template.ContractIntroduction))
                {
                    template.ContractIntroduction = "أنه في يوم {ContractDateArabic} الموافق {ContractDate} م بأمانة العاصمة صنعاء، تم إبرام هذا العقد بين كل من:";
                }

                if (string.IsNullOrEmpty(template.FirstPartyTemplate))
                {
                    template.FirstPartyTemplate = "الاسم: {DriverName}، رقم البطاقة الشخصية: {NationalId}، مكان الإصدار: {IssuePlace}، تاريخ الإصدار: {IssueDate}";
                }

                if (string.IsNullOrEmpty(template.SecondPartyTemplate))
                {
                    template.SecondPartyTemplate = "الصندوق الاجتماعي للتنمية - المركز الرئيسي ومقره شارع الشهيد إبراهيم الحمدي (شارع الستين الجنوبي) مقابل الجهاز المركزي للرقابة والمحاسبة، ويمثله في هذا العقد الأستاذ عبدالله علي الديلمي بصفته المدير التنفيذي للصندوق.";
                }

                if (string.IsNullOrEmpty(template.VehicleSpecsTemplate))
                {
                    template.VehicleSpecsTemplate = "أجر الطرف الأول للطرف الثاني سيارة بالمواصفات التالية: نوع السيارة: {VehicleType}، سنة الصنع: {ManufactureYear}، لون السيارة: {VehicleColor}، رقم اللوحة: {VehicleNumber}، رقم رخصة التسيير: {LicenseNumber}، تاريخ إصدار الرخصة: {LicenseIssueDate}";
                }

                if (string.IsNullOrEmpty(template.PurposeTemplate))
                {
                    template.PurposeTemplate = "اتفق الطرفان على استئجار الطرف الثاني وسيلة نقل (سيارة مع سائقها) لمرافقة الأخ/{VisitConductor}، صفته {VisitConductorRank} أثناء تنفيذ المهام الميدانية المطلوبة منه.";
                }

                if (string.IsNullOrEmpty(template.DurationTemplate))
                {
                    template.DurationTemplate = "تاريخ بداية المهمة: {StartDateArabic} الموافق {StartDate}م، تاريخ انتهاء المهمة: {EndDateArabic} الموافق {EndDate}م، إجمالي عدد الأيام: {DaysCount} أيام. تسير المهمة بموجب وثيقة خطة تنفيذ المهمة المرفقة بالعقد.";
                }

                if (string.IsNullOrEmpty(template.OwnershipTemplate))
                {
                    template.OwnershipTemplate = "يقر الطرف الأول بموجب هذا العقد بأنه المالك الشرعي لوسيلة النقل، وأنه حائز على وثائق إثبات الملكية الشرعية سارية المفعول وأنها خالية من أي التزامات شخصية أو عينية أو مطالبات أو حقوق للغير.";
                }

                if (string.IsNullOrEmpty(template.ObligationsTemplate))
                {
                    template.ObligationsTemplate = "يلتزم الطرف الأول بموجب هذا العقد بأن ينفذ العقد بنفسه وأن يبذل في تأديته من العناية مايبذله الشخص المعتاد، والامتثال بأخلاقيات ومدونة سلوك الصندوق الاجتماعي للتنمية أثناء الرحلة، والحرص على سلامة الركاب والالتزام بكافة قواعد وآداب المرور وقانونه، وعدم نقل أي ركاب آخرين عدا الطرف الثاني أثناء أداء المهام الميدانية، واصطحاب كافة وثائق الملكية أثناء تنفيذ المهمة، والصيانة الدورية لوسيلة النقل وفحصها قبل تنفيذ المهمة، وسرعة الإبلاغ عن أي حوادث تحدث أثناء قيادة المركبة، واصطحاب أدوات السلامة مثل طفاية الحريق وصندوق إسعافات أولية ومثلث التحذير وعدة صيانة السيارة.";
                }

                // تحديث تاريخ التعديل
                template.LastModified = DateTime.Now;

                // حفظ التغييرات
                await _context.SaveChangesAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم تحديث قوالب العقود بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث قوالب العقود: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إصلاح جميع النصوص الموجودة في قاعدة البيانات
        /// </summary>
        public async Task FixExistingContractTextsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء إصلاح النصوص الموجودة...");

                // جلب جميع القوالب
                var templates = await _context.ContractTemplates.ToListAsync();

                foreach (var template in templates)
                {
                    // إصلاح قالب السعر
                    if (!string.IsNullOrEmpty(template.PriceTemplate))
                    {
                        // إزالة أي تنسيق خاطئ للمبلغ
                        template.PriceTemplate = template.PriceTemplate
                            .Replace("{TotalPrice}.00", "{TotalPrice}")
                            .Replace("35200.00", "{TotalPrice}")
                            .Replace("35,200.00", "{TotalPrice}");

                        // التأكد من وجود الأقواس حول المبلغ
                        if (template.PriceTemplate.Contains("{TotalPrice}") && !template.PriceTemplate.Contains("({TotalPrice})"))
                        {
                            template.PriceTemplate = template.PriceTemplate.Replace("{TotalPrice}", "({TotalPrice})");
                        }
                    }

                    template.LastModified = DateTime.Now;
                }

                await _context.SaveChangesAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم إصلاح النصوص الموجودة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إصلاح النصوص: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// التحقق من صحة قوالب العقود
        /// </summary>
        public async Task<bool> ValidateContractTemplatesAsync()
        {
            try
            {
                var templates = await _context.ContractTemplates.ToListAsync();

                foreach (var template in templates)
                {
                    // التحقق من قالب السعر
                    if (string.IsNullOrEmpty(template.PriceTemplate) || 
                        !template.PriceTemplate.Contains("{TotalPrice}"))
                    {
                        System.Diagnostics.Debug.WriteLine("❌ قالب السعر غير صحيح");
                        return false;
                    }

                    // التحقق من عدم وجود مبالغ ثابتة
                    if (template.PriceTemplate.Contains(".00") || 
                        System.Text.RegularExpressions.Regex.IsMatch(template.PriceTemplate, @"\d+\.\d+"))
                    {
                        System.Diagnostics.Debug.WriteLine("❌ قالب السعر يحتوي على مبالغ ثابتة");
                        return false;
                    }
                }

                System.Diagnostics.Debug.WriteLine("✅ جميع قوالب العقود صحيحة");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من القوالب: {ex.Message}");
                return false;
            }
        }
    }
}
